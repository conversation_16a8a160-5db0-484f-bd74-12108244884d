cmake_minimum_required(VERSION 3.16)
project(libkskyb)

set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS ON)  # Enable GNU extensions for gnu++11
set(CMAKE_C_STANDARD 99)

# Compile flags setting (reflecting makefile's -fPIC option)
set(CMAKE_POSITION_INDEPENDENT_CODE ON)
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fPIC -w")
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fPIC -w")

# Include directory setting
include_directories(inc)

# Output directory setting
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/lib)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/lib)

# Create lib and obj directories
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/lib)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/obj)

# qdata library
add_library(libqdata STATIC src/qdata.cpp)
set_target_properties(libqdata PROPERTIES OUTPUT_NAME qdata)

# ksqueue library
add_library(libksqueue STATIC src/ksqueue.cpp)
set_target_properties(libksqueue PROPERTIES OUTPUT_NAME ksqueue)

# kqueue library (C source)
add_library(libkqueue STATIC src/kqueue.c)
set_target_properties(libkqueue PROPERTIES OUTPUT_NAME kqueue)

# kscommon library
add_library(libkscommon STATIC src/kscommon.cpp)
set_target_properties(libkscommon PROPERTIES OUTPUT_NAME kscommon)

# ksconfig library
add_library(libksconfig STATIC src/ksconfig.cpp)
set_target_properties(libksconfig PROPERTIES OUTPUT_NAME ksconfig)

# ksbase64 library
add_library(libksbase64 STATIC src/ksbase64.cpp)
set_target_properties(libksbase64 PROPERTIES OUTPUT_NAME ksbase64)

# ksqueuetable library (depends on ksqueue)
add_library(libksqueuetable STATIC src/ksqueuetable.cpp)
set_target_properties(libksqueuetable PROPERTIES OUTPUT_NAME ksqueuetable)
add_dependencies(libksqueuetable libksqueue)

# kssocket library
add_library(libkssocket STATIC src/kssocket.cpp)
set_target_properties(libkssocket PROPERTIES OUTPUT_NAME kssocket)

# ksthread library
add_library(libksthread STATIC src/ksthread.cpp)
set_target_properties(libksthread PROPERTIES OUTPUT_NAME ksthread)

# ksseedbyte library (multiple source files)
add_library(libksseedbyte STATIC
    src/ksseedbyte.cpp
    src/Seedx-1.c
)
set_target_properties(libksseedbyte PROPERTIES OUTPUT_NAME ksseedbyte)
# Ensure Seedx-1.c is compiled with C compiler
set_source_files_properties(src/Seedx-1.c PROPERTIES LANGUAGE C)

# kssignal library
add_library(libkssignal STATIC src/kssignal.cpp)
set_target_properties(libkssignal PROPERTIES OUTPUT_NAME kssignal)

# Target group for all libraries
add_custom_target(all_libkskyb ALL
    DEPENDS libqdata libksqueue libkqueue libkscommon libksconfig libksbase64 libksqueuetable libkssocket libksthread libksseedbyte libkssignal
)

# Target to install libraries to $HOME/library
add_custom_target(install_libkskyb
    COMMAND ${CMAKE_COMMAND} -E make_directory $ENV{HOME}/library
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/lib/libqdata.a $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/lib/libksqueue.a $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/lib/libkqueue.a $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/lib/libkscommon.a $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/lib/libksconfig.a $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/lib/libksbase64.a $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/lib/libksqueuetable.a $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/lib/libkssocket.a $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/lib/libksthread.a $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/lib/libksseedbyte.a $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/lib/libkssignal.a $ENV{HOME}/library/
    # Copy header files as well
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/inc/qdata.h $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/inc/ksqueue.h $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/inc/kqueue.h $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/inc/kscommon.h $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/inc/ksconfig.h $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/inc/ksbase64.h $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/inc/ksqueuetable.h $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/inc/kssocket.h $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/inc/ksthread.h $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/inc/ksseedbyte.h $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/inc/kssignal.h $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/inc/seedx.h $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/inc/seedx.tab $ENV{HOME}/library/
    DEPENDS all_libkskyb
    COMMENT "Installing libkskyb libraries and headers to $HOME/library"
)

# clean target
add_custom_target(clean_libkskyb
    COMMAND ${CMAKE_COMMAND} -E remove_directory ${CMAKE_CURRENT_SOURCE_DIR}/obj
    COMMAND ${CMAKE_COMMAND} -E remove_directory ${CMAKE_CURRENT_SOURCE_DIR}/lib
    COMMAND ${CMAKE_COMMAND} -E make_directory ${CMAKE_CURRENT_SOURCE_DIR}/obj
    COMMAND ${CMAKE_COMMAND} -E make_directory ${CMAKE_CURRENT_SOURCE_DIR}/lib
    COMMENT "Cleaning libkskyb build artifacts"
)
